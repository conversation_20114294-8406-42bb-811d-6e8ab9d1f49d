'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from 'components/firebase';
import {
  QuestionnaireFormData,
  QuestionnaireSubmission,
  questionnaireQuestions,
} from 'types/questionnaire';
import { CompactThemeToggle } from 'components/ThemeToggle';
import { ArrowLeft } from 'lucide-react';

// Helper to create the initial form state from the questions configuration
const createInitialFormData = (): QuestionnaireFormData => {
  return {
    missingFeatures: '',
    userFriendliness: '',
    voiceModelCompetence: '',
    errorsEncountered: '',
    improvementAspects: '',
    aestheticDesign: '',
    easeOfNavigation: '',
    subscriptionPrice: '',
    voiceTextInterpretation: '',
    scriptAnnotatingFeature: '',
    interactivityFeedback: '',
    rehearsingMemorizing: '',
    auditionUse: '',
    aiIntroduction: '',
    characterVoiceAssignment: ''
  };
};

export default function QuestionnairePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [formData, setFormData] = useState<QuestionnaireFormData>(createInitialFormData());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Authentication check
  useEffect(() => {
    if (status === 'loading') return;
    if (status === 'unauthenticated') {
      // Don't auto-redirect, let user click sign-in button
      // router.push('/api/auth/signin?callbackUrl=/questionnaire');
    }
  }, [status, router]);

  if (status === 'loading') {
    return <div className="flex justify-center items-center h-screen">Loading session...</div>;
  }
  if (status === 'unauthenticated') {
    return (
      <div className="flex flex-col justify-center items-center h-screen bg-gray-50 dark:bg-gray-900 text-center p-4">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Authentication Required
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          You must be logged in to access the questionnaire.
        </p>
        <button
          onClick={() => window.location.href = '/api/auth/signin?callbackUrl=' + encodeURIComponent(window.location.href)}
          className="bg-blue-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Sign In with Google
        </button>
      </div>
    );
  }

  const handleInputChange = (
    questionId: keyof QuestionnaireFormData,
    value: string | boolean, // `value` is the option for checkboxes, checked status for checkboxes, or input value
    subId?: string // `subId` is the specific option for checkboxes or the rating sub-question ID
  ) => {
    setFormData(prev => {
      const newState = { ...prev };
      const question = questionnaireQuestions.find(q => q.id === questionId)!;

      // REFACTORED: Type-driven state updates, removing hardcoded IDs
      if (question.type === 'ratingScale') {
        const currentRatings = newState[questionId] as Record<string, string>;
        newState[questionId] = { ...currentRatings, [subId!]: value as string };
      } else if (question.type === 'checkbox') {
        const currentArray = prev[questionId] as string[];
        const option = subId!; // For checkboxes, subId holds the option text
        const isChecked = value as boolean;
        newState[questionId] = isChecked
          ? [...currentArray, option]
          : currentArray.filter(item => item !== option);
      } else {
        // Handles 'radio' and 'textarea'
        (newState[questionId] as string) = value as string;
      }
      return newState;
    });

    // Clear validation error for the field being edited
    if (validationErrors[questionId]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[questionId];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    for (const q of questionnaireQuestions) {
      if (!q.required) continue; // Skip validation for optional questions

      const value = formData[q.id];

      // REFACTORED: Type-driven validation logic
      if (q.type === 'ratingScale') {
        if (Object.keys(value).length < q.subQuestions!.length) {
          errors[q.id] = 'Please answer all parts of this question.';
        }
      } else if (q.type === 'checkbox') {
         if ((value as string[]).length === 0) {
            errors[q.id] = 'Please select at least one option.';
         } else if (q.maxSelections && (value as string[]).length > q.maxSelections) {
            errors[q.id] = `Please select no more than ${q.maxSelections} options.`;
         }
      } else if (q.type === 'textarea' || q.type === 'radio') {
        if (typeof value === 'string' && !value.trim()) {
          errors[q.id] = 'This field is required.';
        }
      }

      if (q.type === 'textarea' && q.maxLength && (value as string).length > q.maxLength) {
        errors[q.id] = `Maximum ${q.maxLength} characters allowed.`;
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    // FIX: Scroll to first error now uses the updated validationErrors state
    if (!validateForm()) {
      setSubmitError('Please fix the errors highlighted below before submitting.');
      // Use a timeout to ensure state has updated before trying to find the element
      setTimeout(() => {
        const firstErrorKey = Object.keys(validationErrors)[0];
        if (firstErrorKey) {
            document.getElementById(`question-${firstErrorKey}`)?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 0);
      return;
    }

    if (!session?.user?.email) {
      setSubmitError('User session not found. Please sign in again.');
      return;
    }

    setIsSubmitting(true);

    try {
      const submission: Omit<QuestionnaireSubmission, 'id'> = {
        userId: session.user.email,
        userEmail: session.user.email,
        userName: session.user.name || 'N/A',
        responses: formData,
        timestamp: new Date(), // Client-side timestamp (will be replaced by server)
        metadata: { userAgent: navigator.userAgent, sessionId: session.user.email },
      };

      await addDoc(collection(db, 'questionnaireSubmissions'), { // Using a more specific collection name
        ...submission,
        timestamp: serverTimestamp(), // Overwrites with reliable server time
      });

      setSubmitSuccess(true);
    } catch (error) {
      console.error('Error submitting questionnaire:', error);
      setSubmitError('Failed to submit questionnaire. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  let lastSection = '';
  const renderQuestion = (question: Question) => {
    const error = validationErrors[question.id];
    const showSectionHeader = question.section !== lastSection;
    lastSection = question.section;

    return (
      <React.Fragment key={question.id}>
        {showSectionHeader && (
          <div className="mt-12 mb-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-blue-600 dark:text-blue-400">{question.section}</h2>
          </div>
        )}
        <motion.div id={`question-${question.id}`} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="mb-8">
          <label className="block text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {question.question}
            {question.required && <span className="text-red-500 ml-1">*</span>}
          </label>

          {question.type === 'textarea' && (
            <>
              <textarea
                value={formData[question.id] as string}
                onChange={(e) => handleInputChange(question.id, e.target.value)}
                placeholder={question.placeholder}
                maxLength={question.maxLength}
                rows={4}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white courier-font ${error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
              />
              {question.maxLength && (
                <div className="mt-2 text-sm text-right text-gray-500 dark:text-gray-400">
                  {(formData[question.id] as string).length}/{question.maxLength}
                </div>
              )}
            </>
          )}

          {question.type === 'radio' && question.options?.map(option => (
            <label key={option} className="flex items-center p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors mt-2">
              <input type="radio" name={question.id} value={option} checked={(formData[question.id] as string) === option} onChange={(e) => handleInputChange(question.id, e.target.value)} className="h-4 w-4 text-blue-600 focus:ring-blue-500" />
              <span className="ml-3 text-gray-900 dark:text-white">{option}</span>
            </label>
          ))}

          {question.type === 'checkbox' && question.options?.map(option => {
             const isChecked = (formData[question.id] as string[] || []).includes(option);
             return (
                // FIX: Added a key prop to the label, which is the wrapping element
                <label key={option} className="flex items-center p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors mt-2">
                    <input type="checkbox" checked={isChecked} onChange={(e) => handleInputChange(question.id, e.target.checked, option)} className="h-5 w-5 rounded text-blue-600 focus:ring-blue-500" />
                    <span className="ml-3 text-gray-900 dark:text-white">{option}</span>
                </label>
             );
          })}

          {question.type === 'ratingScale' && question.subQuestions?.map(subQ => (
            <div key={subQ.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg mt-3">
              <p className="font-medium text-gray-800 dark:text-gray-200 mb-3">{subQ.label}</p>
              <div className="flex items-center justify-between sm:justify-around flex-wrap gap-2 px-2">
                <span className="text-xs text-gray-500">Dissatisfied</span>
                {[1, 2, 3, 4, 5].map(val => (
                  <label key={val} className="flex flex-col items-center cursor-pointer p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                    <input type="radio" name={`${question.id}-${subQ.id}`} value={val} checked={(formData[question.id] as Record<string, string>)[subQ.id] === String(val)} onChange={() => handleInputChange(question.id, String(val), subQ.id)} className="h-4 w-4 text-blue-600 focus:ring-blue-500" />
                    <span className="mt-1.5 text-xs text-gray-600 dark:text-gray-400 font-semibold">{val}</span>
                  </label>
                ))}
                <span className="text-xs text-gray-500">Satisfied</span>
              </div>
            </div>
          ))}

          {error && <p className="mt-2 text-sm text-red-600 dark:text-red-400 font-medium">{error}</p>}
        </motion.div>
      </React.Fragment>
    );
  };

  if (submitSuccess) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 text-center p-4">
        <motion.div initial={{ opacity: 0, scale: 0.8 }} animate={{ opacity: 1, scale: 1 }}>
          <h1 className="text-4xl font-bold text-green-600 dark:text-green-400 mb-4">Thank You!</h1>
          <p className="text-lg text-gray-700 dark:text-gray-300">Your feedback has been submitted successfully.</p>
          <button onClick={() => router.push('/')} className="mt-8 px-6 py-2 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors">
            Back to Home
          </button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <header className="flex justify-between items-center mb-8">
            <button onClick={() => router.back()} className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors">
                <ArrowLeft size={20} />
                Back
            </button>
            <CompactThemeToggle />
        </header>

        <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
          <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white mb-2">App Feedback Questionnaire</h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-10">
            We value your opinion. Please take a few minutes to share your thoughts with us.
          </p>
        </motion.div>

        <form onSubmit={handleSubmit} noValidate>
          {questionnaireQuestions.map(renderQuestion)}

          <div className="mt-12 pt-6 border-t border-gray-200 dark:border-gray-700">
            {submitError && (
              <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} className="mb-4 p-4 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 rounded-lg">
                {submitError}
              </motion.div>
            )}
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-blue-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all duration-300"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}