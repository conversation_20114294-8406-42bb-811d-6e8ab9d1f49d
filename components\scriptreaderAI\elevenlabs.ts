import { ElevenLabsClient } from "elevenlabs";

// Type definitions
interface CustomKnowledgeBaseUploadResponse {
  document_id: string;
  knowledge_base_id: string;
}

interface RagIndexResponse {
  status: "SUCCEEDED" | "FAILED" | "INDEXING" | "NOT_INDEXED" | "QUEUED" | string;
  message?: string;
}

interface KnowledgeBaseDocumentDetail {
  document_id: string;
  knowledge_base_id: string;
  status: string;
  name: string;
  mime_type: string;
  source_type: string;
  source_url?: string | null;
  size_bytes: number;
  created_at_unix: number;
  metadata?: Record<string, any>;
  content_chunks_count?: number;
  indexed_chunks_count?: number;
  indexing_status?: RagIndexResponse["status"];
  prompt_injectable?: boolean;
}

// Default values from SDK reference
const DEFAULT_RAG_INDEXING_MODEL = 'e5_mistral_7b_instruct';
const DEFAULT_RAG_EMBEDDING_MODEL = 'e5_mistral_7b_instruct';
const DEFAULT_RAG_MAX_DOCUMENTS_LENGTH = 10000;

/**
 * Creates and configures an instance of the ElevenLabs client
 * @param apiKey - Optional API key to override the environment variable
 * @returns Configured ElevenLabs client instance
 */
export function createElevenLabsClient(apiKey?: string): ElevenLabsClient {
  const key = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';
  if (!key) {
    throw new Error("ElevenLabs API key is required. Set ELEVENLABS_API_KEY in your environment variables.");
  }
  return new ElevenLabsClient({ apiKey: key });
}

/**
 * Tests the connection to ElevenLabs API
 * @param apiKey - Optional API key to override the environment variable
 * @returns Boolean indicating if connection is successful
 */
export async function testElevenLabsConnection(apiKey?: string): Promise<boolean> {
  try {
    console.log("[ELEVENLABS] 🔍 Testing ElevenLabs API connection...");

    const client = createElevenLabsClient(apiKey);

    // Try to fetch voices as a simple API test
    const voices = await client.voices.getAll();

    console.log("[ELEVENLABS] ✅ API connection successful, found", voices.voices?.length || 0, "voices");
    return true;
  } catch (error) {
    console.error("[ELEVENLABS] ❌ API connection failed:", error);
    return false;
  }
}

/**
 * Uploads a document to ElevenLabs knowledge base
 *
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param apiKey - Optional API key to override the environment variable
 * @returns Object containing document_id and knowledge_base_id
 * @throws Error if file type is unsupported, URL fetching fails, or API errors occur
 */
export async function uploadToKnowledgeBase(
  fileUrl: string,
  fileName: string,
  fileType: string,
  apiKey?: string
): Promise<CustomKnowledgeBaseUploadResponse> {
  try {
    const client = createElevenLabsClient(apiKey);

    const supportedTypes = [
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/markdown",
    ];

    if (!supportedTypes.includes(fileType)) {
      throw new Error(`Unsupported file type: ${fileType}. Supported types: ${supportedTypes.join(", ")}`);
    }

    let fileResponse;
    try {
      fileResponse = await fetch(fileUrl);
      if (!fileResponse.ok) {
        throw new Error(`HTTP error ${fileResponse.status}: ${fileResponse.statusText}`);
      }
    } catch (fetchError) {
      throw new Error(`Failed to fetch file: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`);
    }

    let fileBlob;
    try {
      fileBlob = await fileResponse.blob();
      if (fileBlob.size === 0) {
        throw new Error("Fetched file is empty (0 bytes)");
      }
    } catch (blobError) {
      throw new Error(`Failed to process file data: ${blobError instanceof Error ? blobError.message : String(blobError)}`);
    }

    const file = new File([fileBlob], fileName, { type: fileType });

    try {
      const response: any = await client.conversationalAi.addToKnowledgeBase({
        file,
        name: fileName,
      });

      console.log("[ELEVENLABS] Raw SDK response from addToKnowledgeBase:", response);

      let documentId: string;
      let knowledgeBaseId: string;

      if (response?.document_id && response?.knowledge_base_id) {
        documentId = response.document_id;
        knowledgeBaseId = response.knowledge_base_id;
      } else if (response?.id) {
        documentId = response.id;
        knowledgeBaseId = response.knowledge_base_id || "default";
      } else {
        console.error("[ELEVENLABS] Invalid response from addToKnowledgeBase:", response);
        throw new Error("Invalid response from ElevenLabs API during upload: Missing required IDs");
      }

      return {
        document_id: documentId,
        knowledge_base_id: knowledgeBaseId,
      };
    } catch (apiError) {
      if (apiError instanceof Error) {
        throw new Error(`ElevenLabs API error during upload: ${apiError.message}`);
      }
      throw new Error(`Unknown error from ElevenLabs API during upload: ${String(apiError)}`);
    }
  } catch (error) {
    console.error("[ELEVENLABS] Error uploading to ElevenLabs Knowledge Base:", error);
    throw error;
  }
}

/**
 * Triggers RAG indexing for a knowledge base document using manual fetch.
 * This function initiates RAG indexing for the specified document and polls until
 * the indexing process is complete (status: SUCCEEDED) or FAILED.
 *
 * @param documentId - ID of the knowledge base document to index
 * @param apiKey - Optional API key to override the environment variable
 * @param ragIndexingModel - The model to use for RAG indexing
 * @returns RAG index status
 * @throws Error if RAG indexing fails or times out
 */
export async function computeRagIndex(
  documentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL
): Promise<RagIndexResponse> {
  try {
    if (!documentId || typeof documentId !== 'string') {
      throw new Error("Document ID is required and must be a string");
    }

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';
    const indexTriggerUrl = `https://api.elevenlabs.io/v1/convai/knowledge-base/${documentId}/compute-rag-index`;

    console.log(`[ELEVENLABS] Triggering RAG indexing for document ${documentId} with model ${ragIndexingModel}`);

    let response = await fetch(indexTriggerUrl, {
      method: "POST",
      headers: {
        "xi-api-key": apiKeyToUse,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ model: ragIndexingModel }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[ELEVENLABS] RAG indexing trigger/status check failed:`, {
        status: response.status, statusText: response.statusText, url: indexTriggerUrl,
        documentId, errorResponse: errorText
      });
      throw new Error(`Failed to trigger/check RAG indexing: ${response.status} ${response.statusText} - ${errorText}`);
    }

    let result: RagIndexResponse = await response.json();
    console.log(`[ELEVENLABS] RAG indexing initial response:`, result);

    const maxAttempts = 60; // 5 mins timeout
    const pollingInterval = 5000; // Poll every 5 seconds
    let attempts = 0;

    while (result.status.toLowerCase() !== "succeeded" && result.status.toLowerCase() !== "failed" && attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollingInterval));
      console.log(`[ELEVENLABS] Polling RAG indexing status for document ${documentId} (attempt ${attempts}/${maxAttempts})`);

      response = await fetch(indexTriggerUrl, {
        method: "POST",
        headers: {
          "xi-api-key": apiKeyToUse,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ model: ragIndexingModel }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[ELEVENLABS] RAG indexing status poll failed:`, {
          status: response.status, statusText: response.statusText, documentId, attempt: attempts, errorResponse: errorText
        });
        throw new Error(`Failed to poll RAG indexing status: ${response.status} ${response.statusText} - ${errorText}`);
      }

      result = await response.json();
      console.log(`[ELEVENLABS] RAG indexing status (attempt ${attempts}/${maxAttempts}):`, result);
    }

    if (result.status.toLowerCase() === "succeeded") {
      console.log(`[ELEVENLABS] RAG indexing completed successfully for document ${documentId}`);
      return result;
    }
    if (result.status.toLowerCase() === "failed") {
      throw new Error(`RAG indexing failed for document ${documentId}: ${result.message || 'No additional message'}`);
    }
    if (attempts >= maxAttempts) {
      throw new Error(`RAG indexing for document ${documentId} did not complete within ${maxAttempts * pollingInterval / 1000} seconds. Last status: ${result.status}`);
    }

    return result;
  } catch (error) {
    console.error("[ELEVENLABS] Error during RAG indexing (manual fetch):", error);
    throw error;
  }
}

/**
 * Triggers RAG indexing using the ElevenLabs SDK and polls for completion.
 *
 * @param documentId - ID of the knowledge base document to index
 * @param apiKey - Optional API key
 * @param ragIndexingModel - The model to use for RAG indexing
 * @returns RAG index status
 * @throws Error if RAG indexing fails or times out
 */
export async function computeRagIndexViaSDK(
  documentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL
): Promise<RagIndexResponse> {
  try {
    if (!documentId || typeof documentId !== 'string') {
      throw new Error("Document ID is required and must be a string");
    }
    const client = createElevenLabsClient(apiKey);

    console.log(`[ELEVENLABS] Starting RAG indexing via SDK for document ${documentId} with model ${ragIndexingModel}`);

    let response: RagIndexResponse = await client.conversationalAi.ragIndexStatus(
      documentId,
      { model: ragIndexingModel as any }
    );
    console.log("[ELEVENLABS] SDK RAG index initial response:", response);

    const maxAttempts = 60; // 5 mins timeout
    const pollingInterval = 5000; // Poll every 5 seconds
    let attempts = 0;

    while (response.status.toLowerCase() !== "succeeded" && response.status.toLowerCase() !== "failed" && attempts < maxAttempts) {
      attempts++;
      await new Promise((resolve) => setTimeout(resolve, pollingInterval));
      console.log(`[ELEVENLABS] Polling RAG indexing status via SDK (attempt ${attempts}/${maxAttempts}) for document ${documentId}`);
      response = await client.conversationalAi.ragIndexStatus(
        documentId,
        { model: ragIndexingModel as any }
      );
      console.log(`[ELEVENLABS] SDK RAG index status (attempt ${attempts}/${maxAttempts}):`, response);
    }

    if (response.status.toLowerCase() === "succeeded") {
      console.log(`[ELEVENLABS] RAG indexing via SDK completed successfully for document ${documentId}`);
      return response;
    }
    if (response.status.toLowerCase() === "failed") {
      throw new Error(`RAG indexing via SDK failed for document ${documentId}: ${response.message || 'No additional message'}`);
    }
    if (attempts >= maxAttempts) {
      throw new Error(`RAG indexing via SDK for document ${documentId} did not complete within ${maxAttempts * pollingInterval / 1000} seconds. Last status: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error("[ELEVENLABS] Error using SDK for RAG indexing:", error);
    throw error;
  }
}

/**
 * Fetches the agent's configuration
 * @param agentId - ID of the agent to fetch
 * @param apiKey - Optional API key
 * @returns Agent configuration
 */
export async function getAgentConfiguration(agentId: string, apiKey?: string): Promise<any> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    const client = createElevenLabsClient(apiKey);
    const agentConfig = await client.conversationalAi.getAgent(agentId);

    console.log(`[ELEVENLABS] Fetched agent configuration for agent ${agentId}:`, agentConfig);
    return agentConfig;
  } catch (error) {
    console.error(`[ELEVENLABS] Error fetching agent configuration for agent ${agentId}:`, error);
    throw error;
  }
}

/**
 * Configures client tools for the ElevenLabs agent programmatically
 * This ensures the switch_to_script_tab tool is properly registered
 * @param agentId - ID of the agent to configure
 * @param apiKey - Optional API key
 * @returns Updated agent configuration
 */
export async function configureAgentClientTools(agentId: string, apiKey?: string): Promise<any> {
  try {
    console.log(`[ELEVENLABS] 🔧 Configuring client tools for agent ${agentId}...`);

    const client = createElevenLabsClient(apiKey);
    const currentConfig = await getAgentConfiguration(agentId, apiKey);

    // Define the script readiness acknowledgment client tool
    const scriptReadinessTool = {
      name: "switch_to_script_tab",
      type: "client",
      description: "Call this function when the user confirms they are ready to begin rehearsal. This will acknowledge their readiness and inform them to manually navigate to the Script tab if they want to view their script content during rehearsal.",
      parameters: {
        type: "object",
        properties: {
          ready: {
            type: "boolean",
            description: "Set to true when user has confirmed they are ready to begin rehearsal",
            default: true
          }
        },
        required: ["ready"]
      },
      wait_for_response: true
    };

    // Get existing tools and filter out any existing switch_to_script_tab tools
    const existingTools = currentConfig.conversation_config?.tools || [];
    const filteredTools = existingTools.filter((tool: any) =>
      tool.name !== 'switch_to_script_tab' && tool.name !== 'load_script_for_rehearsal'
    );

    // Add the new tool
    const updatedTools = [...filteredTools, scriptReadinessTool];

    console.log(`[ELEVENLABS] 🔧 Tool configuration:`, {
      existingToolsCount: existingTools.length,
      filteredToolsCount: filteredTools.length,
      updatedToolsCount: updatedTools.length,
      newTool: scriptReadinessTool
    });

    // Update the agent configuration
    const patchBody = {
      conversation_config: {
        ...currentConfig.conversation_config,
        tools: updatedTools
      }
    };

    console.log(`[ELEVENLABS] 🔧 Updating agent with client tools...`);
    const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);

    console.log(`[ELEVENLABS] ✅ Client tools configured successfully for agent ${agentId}`);

    // Wait for propagation
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the update
    const verificationConfig = await getAgentConfiguration(agentId, apiKey);
    const toolsAfterUpdate = verificationConfig.conversation_config?.tools || [];
    const hasScriptTool = toolsAfterUpdate.some((tool: any) => tool.name === 'switch_to_script_tab');

    console.log(`[ELEVENLABS] 🔍 Tool verification:`, {
      toolsCount: toolsAfterUpdate.length,
      hasScriptTool,
      tools: toolsAfterUpdate.map((t: any) => ({ name: t.name, type: t.type }))
    });

    return updateResult;
  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Error configuring client tools for agent ${agentId}:`, error);
    throw error;
  }
}

/**
 * Extracts the agent name from agent configuration by using the voice display name
 * @param agentConfig - Agent configuration object
 * @returns Agent name based on voice display name or fallback
 */
export function extractAgentName(agentConfig: any): string {
  console.log(`[ELEVENLABS] 🔍 Extracting agent name from voice configuration...`);

  // First, try to get the current voice ID from the agent configuration
  const voiceId = agentConfig?.conversation_config?.tts?.voice_id ||
                  agentConfig?.conversation_config?.agent?.voice_id ||
                  agentConfig?.tts?.voice_id ||
                  agentConfig?.voice_id;

  console.log(`[ELEVENLABS] Voice ID found in agent config: "${voiceId}"`);

  if (voiceId) {
    // Import the voice utility function to get voice display name
    try {
      // We'll need to import this dynamically or pass it as a parameter
      // For now, let's extract the voice name using the voice mapping
      const voiceName = getVoiceDisplayName(voiceId);
      if (voiceName) {
        console.log(`[ELEVENLABS] 🏷️ Agent name extracted from voice: "${voiceName}" (voice ID: ${voiceId})`);
        return voiceName;
      }
    } catch (error) {
      console.warn(`[ELEVENLABS] ⚠️ Failed to get voice display name for voice ID ${voiceId}:`, error);
    }
  }

  // Fallback to agent configuration name if voice name extraction fails
  console.log(`[ELEVENLABS] 🔄 Falling back to agent configuration name...`);
  const possibleNames = [
    agentConfig?.name,
    agentConfig?.display_name,
    agentConfig?.agent_name,
    agentConfig?.conversation_config?.agent?.name,
    agentConfig?.conversation_config?.name,
    agentConfig?.metadata?.name
  ];

  for (let i = 0; i < possibleNames.length; i++) {
    const name = possibleNames[i];
    if (name && typeof name === 'string' && name.trim().length > 0) {
      console.log(`[ELEVENLABS] 🏷️ Agent name extracted from config fallback: "${name}"`);
      return name.trim();
    }
  }

  console.log(`[ELEVENLABS] ⚠️ No agent name found, using final fallback`);
  return 'CastMate Assistant';
}

/**
 * Gets the display name for a voice ID
 * @param voiceId - The voice ID to look up
 * @returns Voice display name or null if not found
 */
function getVoiceDisplayName(voiceId: string): string | null {
  // Voice mapping based on the voiceUtils.ts file
  const voiceMap: Record<string, string> = {
    'rCuVrCHOUMY3OwyJBJym': 'Mia',
    'QQutlXbwqnU9C4Zprxnn': 'Morgan',
    'P7x743VjyZEOihNNygQ9': 'Dakota',
    'kmSVBPu7loj4ayNinwWM': 'Archie',
    'AeRdCCKzvd23BpJoofzx': 'Nathaniel',
    'vVnXvLYPFjIyE2YrjUBE': 'Brad'
  };

  const voiceName = voiceMap[voiceId];
  console.log(`[ELEVENLABS] Voice mapping lookup: ${voiceId} -> ${voiceName || 'not found'}`);
  return voiceName || null;
}

/**
 * Updates the agent's prompt configuration
 * @param agentId - ID of the agent to update
 * @param newPrompt - New prompt text to set for the agent
 * @param apiKey - Optional API key
 * @returns Result of the agent update operation
 */
export async function updateAgentPrompt(
  agentId: string,
  newPrompt: string,
  apiKey?: string
): Promise<any> {
  try {
    // Enhanced parameter validation
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!newPrompt || typeof newPrompt !== 'string') {
      throw new Error("Prompt is required and must be a string");
    }

    console.log(`[ELEVENLABS] Starting prompt update process for agent ${agentId}`);
    console.log(`[ELEVENLABS] New prompt length: ${newPrompt.length} characters`);
    console.log(`[ELEVENLABS] API key provided: ${!!apiKey}`);

    // Step 1: Fetch current agent configuration
    console.log(`[ELEVENLABS] Step 1: Fetching current agent configuration...`);
    let currentAgentConfig;
    try {
      currentAgentConfig = await getAgentConfiguration(agentId, apiKey);
      console.log(`[ELEVENLABS] Current agent config retrieved successfully`);
    } catch (configError) {
      console.error(`[ELEVENLABS] Failed to fetch agent configuration:`, configError);
      throw new Error(`Failed to fetch agent configuration: ${configError instanceof Error ? configError.message : String(configError)}`);
    }

    // Step 2: Prepare the update payload
    console.log(`[ELEVENLABS] Step 2: Preparing prompt update payload...`);
    const conversationConfig = currentAgentConfig.conversation_config || currentAgentConfig;

    if (!conversationConfig.agent) {
      console.warn(`[ELEVENLABS] Agent configuration missing 'agent' property, creating it`);
    }

    // Update prompt in the agent configuration
    const patchBody = {
      conversation_config: {
        ...conversationConfig,
        agent: {
          ...conversationConfig.agent,
          prompt: {
            ...conversationConfig.agent?.prompt,
            prompt: newPrompt
          }
        }
      }
    };

    console.log(`[ELEVENLABS] Prompt update payload prepared`);
    console.log(`[ELEVENLABS] Original prompt length: ${conversationConfig.agent?.prompt?.prompt?.length || 0} characters`);
    console.log(`[ELEVENLABS] New prompt length: ${newPrompt.length} characters`);

    // Step 3: Execute the update
    console.log(`[ELEVENLABS] Step 3: Executing agent prompt update...`);
    let client;
    try {
      client = createElevenLabsClient(apiKey);
    } catch (clientError) {
      console.error(`[ELEVENLABS] Failed to create ElevenLabs client:`, clientError);
      throw new Error(`Failed to create ElevenLabs client: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
    }

    let updateResult;
    try {
      updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);
      console.log(`[ELEVENLABS] Agent prompt update API call successful. Result:`, updateResult);
    } catch (updateError) {
      console.error(`[ELEVENLABS] Agent prompt update API call failed:`, updateError);
      throw new Error(`Agent prompt update API call failed: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
    }

    // Step 4: Wait for propagation and verify
    console.log(`[ELEVENLABS] Step 4: Waiting for update to propagate...`);
    await new Promise(resolve => setTimeout(resolve, 1500));

    console.log(`[ELEVENLABS] Step 5: Verifying prompt update...`);
    try {
      const finalAgentConfig = await getAgentConfiguration(agentId, apiKey);
      const finalConversationConfig = finalAgentConfig.conversation_config || finalAgentConfig;

      // Check if prompt was updated
      const finalPrompt = finalConversationConfig.agent?.prompt?.prompt;
      const promptUpdated = finalPrompt === newPrompt;

      console.log(`[ELEVENLABS] Prompt verification results:`, {
        expected_length: newPrompt.length,
        final_length: finalPrompt?.length || 0,
        prompt_updated: promptUpdated
      });

      if (promptUpdated) {
        console.log(`[ELEVENLABS] ✅ Prompt update verification successful!`);
      } else {
        console.error(`[ELEVENLABS] ❌ Prompt update verification failed`);
        console.warn(`[ELEVENLABS] This may indicate a propagation delay or API issue`);
      }

    } catch (verificationError) {
      console.warn(`[ELEVENLABS] Could not verify prompt update:`, verificationError);
      // Don't throw here as the update might have succeeded
    }

    console.log(`[ELEVENLABS] ✅ Prompt update process completed successfully`);
    return updateResult;

  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Prompt update process failed for agent ${agentId}:`, error);

    // Provide more specific error information
    let errorMessage = `Failed to update prompt for agent ${agentId}`;
    const originalError = error instanceof Error ? error.message : String(error);

    if (originalError.includes("authentication") || originalError.includes("API key") || originalError.includes("401")) {
      errorMessage += " - Authentication failed. Please check your API key.";
    } else if (originalError.includes("404") || originalError.includes("not found")) {
      errorMessage += " - Agent not found. Please check the agent ID.";
    } else if (originalError.includes("400") || originalError.includes("bad request")) {
      errorMessage += " - Invalid request. Please check the prompt content.";
    } else if (originalError.includes("network") || originalError.includes("fetch")) {
      errorMessage += " - Network error. Please check your connection.";
    } else {
      errorMessage += ` - ${originalError}`;
    }

    throw new Error(errorMessage);
  }
}

/**
 * Updates the agent's voice configuration
 * @param agentId - ID of the agent to update
 * @param voiceId - ID of the voice to set for the agent
 * @param apiKey - Optional API key
 * @returns Result of the agent update operation
 */
export async function updateAgentVoice(
  agentId: string,
  voiceId: string,
  apiKey?: string
): Promise<any> {
  try {
    // Enhanced parameter validation
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!voiceId || typeof voiceId !== 'string') {
      throw new Error("Voice ID is required and must be a string");
    }

    console.log(`[ELEVENLABS] Starting voice update process for agent ${agentId} to voice ${voiceId}`);
    console.log(`[ELEVENLABS] API key provided: ${!!apiKey}`);

    // Step 1: Fetch current agent configuration
    console.log(`[ELEVENLABS] Step 1: Fetching current agent configuration...`);
    let currentAgentConfig;
    try {
      currentAgentConfig = await getAgentConfiguration(agentId, apiKey);
      console.log(`[ELEVENLABS] Current agent config retrieved successfully`);
      console.log(`[ELEVENLABS] Current agent config structure:`, JSON.stringify(currentAgentConfig, null, 2));
    } catch (configError) {
      console.error(`[ELEVENLABS] Failed to fetch agent configuration:`, configError);
      throw new Error(`Failed to fetch agent configuration: ${configError instanceof Error ? configError.message : String(configError)}`);
    }

    // Step 2: Prepare the update payload
    console.log(`[ELEVENLABS] Step 2: Preparing voice update payload...`);
    const conversationConfig = currentAgentConfig.conversation_config || currentAgentConfig;

    if (!conversationConfig.agent) {
      console.warn(`[ELEVENLABS] Agent configuration missing 'agent' property, creating it`);
    }

    if (!conversationConfig.tts) {
      console.warn(`[ELEVENLABS] Agent configuration missing 'tts' property, creating it`);
    }

    // Update voice_id in BOTH locations where ElevenLabs expects it
    const patchBody = {
      conversation_config: {
        ...conversationConfig,
        // Update the TTS voice_id (this is what ElevenLabs actually uses for voice synthesis)
        tts: {
          ...conversationConfig.tts,
          voice_id: voiceId
        },
        // Also update the agent voice_id (for consistency)
        agent: {
          ...conversationConfig.agent,
          voice_id: voiceId
        }
      }
    };

    console.log(`[ELEVENLABS] Voice update targets:`, {
      tts_voice_id: voiceId,
      agent_voice_id: voiceId,
      current_tts_voice: conversationConfig.tts?.voice_id,
      current_agent_voice: conversationConfig.agent?.voice_id
    });

    console.log(`[ELEVENLABS] Prepared PATCH body:`, JSON.stringify(patchBody, null, 2));

    // Step 3: Execute the update
    console.log(`[ELEVENLABS] Step 3: Executing agent voice update...`);
    let client;
    try {
      client = createElevenLabsClient(apiKey);
    } catch (clientError) {
      console.error(`[ELEVENLABS] Failed to create ElevenLabs client:`, clientError);
      throw new Error(`Failed to create ElevenLabs client: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
    }

    let updateResult;
    try {
      updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);
      console.log(`[ELEVENLABS] Agent update API call successful. Result:`, updateResult);
    } catch (updateError) {
      console.error(`[ELEVENLABS] Agent update API call failed:`, updateError);
      throw new Error(`Agent update API call failed: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
    }

    // Step 4: Wait for propagation and verify
    console.log(`[ELEVENLABS] Step 4: Waiting for update to propagate...`);
    await new Promise(resolve => setTimeout(resolve, 1500));

    console.log(`[ELEVENLABS] Step 5: Verifying voice update...`);
    try {
      const finalAgentConfig = await getAgentConfiguration(agentId, apiKey);
      const finalConversationConfig = finalAgentConfig.conversation_config || finalAgentConfig;

      // Check BOTH locations where voice_id should be updated
      const finalTtsVoiceId = finalConversationConfig.tts?.voice_id;
      const finalAgentVoiceId = finalConversationConfig.agent?.voice_id;

      console.log(`[ELEVENLABS] Voice verification results:`, {
        expected_voice_id: voiceId,
        final_tts_voice_id: finalTtsVoiceId,
        final_agent_voice_id: finalAgentVoiceId,
        tts_match: finalTtsVoiceId === voiceId,
        agent_match: finalAgentVoiceId === voiceId
      });

      // Primary check: TTS voice_id (this is what actually matters for voice synthesis)
      if (finalTtsVoiceId === voiceId) {
        console.log(`[ELEVENLABS] ✅ Voice update verification successful! TTS voice_id updated correctly.`);
        if (finalAgentVoiceId === voiceId) {
          console.log(`[ELEVENLABS] ✅ Agent voice_id also updated correctly.`);
        } else {
          console.warn(`[ELEVENLABS] ⚠️ Agent voice_id not updated, but TTS voice_id is correct (this is the important one).`);
        }
      } else {
        console.error(`[ELEVENLABS] ❌ Voice update verification failed - TTS voice_id mismatch`);
        console.error(`[ELEVENLABS] Expected: ${voiceId}, Got: ${finalTtsVoiceId}`);
        console.warn(`[ELEVENLABS] This may indicate a propagation delay or API issue`);
      }

      console.log(`[ELEVENLABS] Final agent configuration:`, JSON.stringify(finalAgentConfig, null, 2));
    } catch (verificationError) {
      console.warn(`[ELEVENLABS] Could not verify voice update:`, verificationError);
      // Don't throw here as the update might have succeeded
    }

    console.log(`[ELEVENLABS] ✅ Voice update process completed successfully`);
    return updateResult;

  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Voice update process failed for agent ${agentId}:`, error);

    // Provide more specific error information
    let errorMessage = `Failed to update voice for agent ${agentId}`;
    const originalError = error instanceof Error ? error.message : String(error);

    if (originalError.includes("authentication") || originalError.includes("API key") || originalError.includes("401")) {
      errorMessage += " - Authentication failed. Please check your API key.";
    } else if (originalError.includes("404") || originalError.includes("not found")) {
      errorMessage += " - Agent not found. Please check the agent ID.";
    } else if (originalError.includes("400") || originalError.includes("bad request")) {
      errorMessage += " - Invalid request. Please check the voice ID.";
    } else if (originalError.includes("network") || originalError.includes("fetch")) {
      errorMessage += " - Network error. Please check your connection.";
    } else {
      errorMessage += ` - ${originalError}`;
    }

    throw new Error(errorMessage);
  }
}

/**
 * Associates a knowledge base document with an agent and configures RAG.
 * Updates agent's `prompt.knowledge_base` array and `prompt.rag` settings.
 *
 * @param agentId - ID of the agent
 * @param knowledgeBaseDocId - ID of the knowledge base document
 * @param apiKey - Optional API key
 * @param ragEmbeddingModel - Embedding model for RAG
 * @param ragMaxDocumentsLength - Max documents length for RAG
 * @param documentName - Optional name of the document
 * @returns Result of the agent update operation
 */
export async function updateAgentKnowledgeBase(
  agentId: string,
  knowledgeBaseDocId: string,
  apiKey?: string,
  ragEmbeddingModel: string = DEFAULT_RAG_EMBEDDING_MODEL,
  ragMaxDocumentsLength: number = DEFAULT_RAG_MAX_DOCUMENTS_LENGTH,
  documentName?: string
): Promise<any> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!knowledgeBaseDocId || typeof knowledgeBaseDocId !== 'string') {
      throw new Error("Knowledge base document ID (document_id) is required and must be a string");
    }

    console.log(`[ELEVENLABS] Associating document ${knowledgeBaseDocId} with agent ${agentId} and configuring RAG.`);

    const currentAgentConfig = await getAgentConfiguration(agentId, apiKey);
    console.log(`[ELEVENLABS] Current agent config:`, JSON.stringify(currentAgentConfig, null, 2));

    const conversationConfig = currentAgentConfig.conversation_config || currentAgentConfig;

    const existingKnowledgeBase = conversationConfig.agent?.prompt?.knowledge_base || [];

    const documentExists = existingKnowledgeBase.some((doc: any) =>
      doc.id === knowledgeBaseDocId || doc.document_id === knowledgeBaseDocId
    );

    let updatedKnowledgeBase = [...existingKnowledgeBase];

    if (!documentExists) {
      let docName = documentName;
      if (!docName) {
        try {
          const docDetails = await getKnowledgeBaseDocument(knowledgeBaseDocId, apiKey);
          docName = docDetails.name;
        } catch (error) {
          console.warn(`[ELEVENLABS] Could not fetch document name for ${knowledgeBaseDocId}, using ID as fallback`);
          docName = knowledgeBaseDocId;
        }
      }

      updatedKnowledgeBase.push({
        type: "file",
        name: docName,
        id: knowledgeBaseDocId,
        usage_mode: "auto"
      });
      console.log(`[ELEVENLABS] Adding document ${knowledgeBaseDocId} (${docName}) to agent's knowledge base`);
    } else {
      updatedKnowledgeBase = updatedKnowledgeBase.map((doc: any) => {
        if (doc.id === knowledgeBaseDocId || doc.document_id === knowledgeBaseDocId) {
          return {
            type: doc.type || "file",
            name: doc.name || documentName || knowledgeBaseDocId,
            id: doc.id || knowledgeBaseDocId,
            usage_mode: "auto"
          };
        }
        return doc;
      });
      console.log(`[ELEVENLABS] Document ${knowledgeBaseDocId} already exists, ensuring usage_mode is 'auto'`);
    }

    const patchBody = {
      conversation_config: {
        ...conversationConfig,
        agent: {
          ...conversationConfig.agent,
          prompt: {
            ...conversationConfig.agent?.prompt,
            knowledge_base: updatedKnowledgeBase,
            rag: {
              enabled: true,
              embedding_model: ragEmbeddingModel,
              max_documents_length: ragMaxDocumentsLength,
            },
          },
        },
      },
    };

    const client = createElevenLabsClient(apiKey);
    console.log(`[ELEVENLABS] Attempting to update agent ${agentId} with PATCH body:`, JSON.stringify(patchBody, null, 2));

    const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);
    console.log(`[ELEVENLABS] Successfully updated agent ${agentId}. Result:`, updateResult);

    await new Promise(resolve => setTimeout(resolve, 2000));

    const finalAgentConfig = await getAgentConfiguration(agentId, apiKey);
    console.log(`[ELEVENLABS] Agent ${agentId} configuration after update:`, JSON.stringify(finalAgentConfig, null, 2));

    const finalConversationConfig = finalAgentConfig.conversation_config || finalAgentConfig;
    const finalKnowledgeBase = finalConversationConfig.agent?.prompt?.knowledge_base || [];
    const documentWasAdded = finalKnowledgeBase.some((doc: any) =>
      doc.id === knowledgeBaseDocId || doc.document_id === knowledgeBaseDocId
    );

    if (!documentWasAdded) {
      console.error(`[ELEVENLABS] CRITICAL: Document ${knowledgeBaseDocId} was NOT found in agent's knowledge base after update!`);
      console.error(`[ELEVENLABS] Final knowledge base contains:`, finalKnowledgeBase);
      throw new Error(`Document ${knowledgeBaseDocId} was not successfully added to agent ${agentId}'s knowledge base. Update may have failed silently.`);
    }

    console.log(`[ELEVENLABS] SUCCESS: Document ${knowledgeBaseDocId} confirmed in agent's knowledge base with ${finalKnowledgeBase.length} total documents`);
    return updateResult;

  } catch (error) {
    console.error("[ELEVENLABS] Error updating agent knowledge base and RAG settings:", error);
    throw new Error(
      `Failed to associate document ${knowledgeBaseDocId} and configure RAG for agent ${agentId}. ` +
      `Error: ${error instanceof Error ? error.message : String(error)}. ` +
      `Manual check may be required via the ElevenLabs dashboard.`
    );
  }
}

/**
 * Retrieves a list of all knowledge bases for the account.
 * @param apiKey - Optional API key
 * @returns List of knowledge bases
 */
export async function getKnowledgeBaseList(apiKey?: string): Promise<any[]> {
  try {
    const client = createElevenLabsClient(apiKey);
    const knowledgeBases: any = await client.conversationalAi.getKnowledgeBaseList();
    console.log("[ELEVENLABS] Raw knowledge base list response:", knowledgeBases);
    if (!Array.isArray(knowledgeBases)) {
      console.warn("[ELEVENLABS] Unexpected response from getKnowledgeBaseList:", knowledgeBases);
      return [];
    }
    return knowledgeBases;
  } catch (error) {
    console.error("[ELEVENLABS] Error getting knowledge base list:", error);
    throw error;
  }
}

/**
 * Retrieves a knowledge base document by ID.
 * @param documentId - ID of the document to retrieve
 * @param apiKey - Optional API key
 * @returns Knowledge base document details
 */
export async function getKnowledgeBaseDocument(
  documentId: string,
  apiKey?: string,
  knowledgeBaseIdForContext?: string
): Promise<KnowledgeBaseDocumentDetail> {
  if (!documentId || typeof documentId !== 'string') {
    throw new Error("Document ID is required and must be a string");
  }

  try {
    const client = createElevenLabsClient(apiKey);
    const document: any = await client.conversationalAi.getKnowledgeBaseDocumentById(documentId);
    console.log("[ELEVENLABS] Raw document response from getKnowledgeBaseDocumentById:", document);

    const mappedDocument: KnowledgeBaseDocumentDetail = {
      document_id: document.document_id || document.id || documentId,
      knowledge_base_id: document.knowledge_base_id || knowledgeBaseIdForContext || "unknown",
      status: document.status || "unknown",
      name: document.name || "unknown",
      mime_type: document.mime_type || document.type || "unknown",
      source_type: document.source_type || "upload",
      source_url: document.source_url || null,
      size_bytes: document.size_bytes || document.size || 0,
      created_at_unix: document.created_at_unix || document.created_at || Math.floor(Date.now() / 1000),
      metadata: document.metadata || {},
      content_chunks_count: document.content_chunks_count || 0,
      indexed_chunks_count: document.indexed_chunks_count || 0,
      indexing_status: document.indexing_status || "NOT_INDEXED",
      prompt_injectable: document.prompt_injectable || false,
    };

    if (!mappedDocument.document_id) {
      throw new Error(`Document with ID ${documentId} not found or has invalid format.`);
    }
    return mappedDocument;
  } catch (error) {
    console.error(`[ELEVENLABS] Error getting knowledge base document ${documentId}:`, error);
    throw error;
  }
}

/**
 * Complete workflow: Upload to Knowledge Base + RAG Indexing + Agent Association
 *
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param agentId - ID of the agent to associate with the document
 * @param apiKey - Optional API key
 * @param ragIndexingModel - Model for RAG indexing
 * @param ragEmbeddingModel - Embedding model for agent's RAG config
 * @param ragMaxDocumentsLength - Max documents length for agent's RAG config
 * @param useSdkForRagIndexing - Whether to use SDK or manual fetch for RAG indexing
 * @returns Upload result with metadata
 */
export async function uploadAndIndexForRehearsal(
  fileUrl: string,
  fileName: string,
  fileType: string,
  agentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL,
  ragEmbeddingModel: string = DEFAULT_RAG_EMBEDDING_MODEL,
  ragMaxDocumentsLength: number = DEFAULT_RAG_MAX_DOCUMENTS_LENGTH,
  useSdkForRagIndexing: boolean = true
): Promise<{
  knowledgeBaseDocId: string;
  knowledgeBaseId: string;
  prompt_injectable: boolean;
  ragIndexStatus: RagIndexResponse["status"];
  agentUpdated: boolean;
  uploaded_at: string;
}> {
  try {
    console.log(`[ELEVENLABS] Starting complete upload workflow for ${fileName} to agent ${agentId}`);

    console.log(`[ELEVENLABS] Step 1: Uploading to Knowledge Base...`);
    const kbUploadResponse = await uploadToKnowledgeBase(fileUrl, fileName, fileType, apiKey);
    console.log(`[ELEVENLABS] Knowledge Base upload successful: doc_id=${kbUploadResponse.document_id}, kb_id=${kbUploadResponse.knowledge_base_id}`);

    console.log(`[ELEVENLABS] Step 2: Fetching document details...`);
    const docDetails = await getKnowledgeBaseDocument(kbUploadResponse.document_id, apiKey, kbUploadResponse.knowledge_base_id);
    const promptInjectable = docDetails.prompt_injectable === true;
    console.log(`[ELEVENLABS] Document prompt_injectable: ${promptInjectable}, indexing_status: ${docDetails.indexing_status}`);

    console.log(`[ELEVENLABS] Step 3: Starting RAG indexing (SDK: ${useSdkForRagIndexing})...`);
    let ragIndexResult: RagIndexResponse;
    if (useSdkForRagIndexing) {
      ragIndexResult = await computeRagIndexViaSDK(kbUploadResponse.document_id, apiKey, ragIndexingModel);
    } else {
      ragIndexResult = await computeRagIndex(kbUploadResponse.document_id, apiKey, ragIndexingModel);
    }
    console.log(`[ELEVENLABS] RAG indexing completed: ${ragIndexResult.status} (${ragIndexResult.message || ''})`);
    if (ragIndexResult.status.toLowerCase() !== "succeeded") {
      throw new Error(`RAG indexing did not succeed. Status: ${ragIndexResult.status}, Message: ${ragIndexResult.message || 'N/A'}`);
    }

    console.log(`[ELEVENLABS] Step 4: Associating with agent ${agentId} and configuring RAG settings...`);
    await updateAgentKnowledgeBase(agentId, kbUploadResponse.document_id, apiKey, ragEmbeddingModel, ragMaxDocumentsLength, fileName);
    console.log(`[ELEVENLABS] Agent association and RAG configuration completed successfully`);

    const result = {
      knowledgeBaseDocId: kbUploadResponse.document_id,
      knowledgeBaseId: kbUploadResponse.knowledge_base_id,
      prompt_injectable: promptInjectable,
      ragIndexStatus: ragIndexResult.status,
      agentUpdated: true,
      uploaded_at: new Date().toISOString()
    };

    console.log(`[ELEVENLABS] Complete upload workflow finished successfully for ${fileName}`, result);
    return result;

  } catch (error) {
    console.error(`[ELEVENLABS] Complete upload workflow failed for ${fileName}:`, error);
    throw error;
  }
}

/**
 * Debug function to verify document exists before indexing
 */
export async function verifyDocumentExists(
  documentId: string,
  apiKey?: string
): Promise<boolean> {
  try {
    await getKnowledgeBaseDocument(documentId, apiKey);
    console.log(`[ELEVENLABS] Document ${documentId} verification successful (exists).`);
    return true;
  } catch (error) {
    console.error(`[ELEVENLABS] Document ${documentId} verification failed:`, error);
    return false;
  }
}

/**
 * Test function for RAG indexing endpoints.
 * @param documentId - ID of an existing document to test RAG indexing with
 * @param apiKey - Optional API key
 * @param ragIndexingModel - Model for RAG indexing
 */
export async function testRagIndexingEndpoints(
  documentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL
): Promise<any> {
  try {
    console.log(`[ELEVENLABS] Testing RAG indexing endpoints for document ${documentId} using model ${ragIndexingModel}`);

    const docExists = await verifyDocumentExists(documentId, apiKey);
    if (!docExists) {
      console.warn(`[ELEVENLABS] Document ${documentId} does not exist or couldn't be fetched. Test may provide limited info.`);
    }

    console.log(`[ELEVENLABS] Testing with computeRagIndex (manual fetch)...`);
    const manualResult = await computeRagIndex(documentId, apiKey, ragIndexingModel);
    console.log(`[ELEVENLABS] Manual fetch RAG indexing test result:`, manualResult);

    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log(`[ELEVENLABS] Testing with computeRagIndexViaSDK...`);
    const sdkResult = await computeRagIndexViaSDK(documentId, apiKey, ragIndexingModel);
    console.log(`[ELEVENLABS] SDK RAG indexing test result:`, sdkResult);

    return {
      documentId,
      documentVerified: docExists,
      manualFetchResult: manualResult,
      sdkResult: sdkResult,
    };

  } catch (error) {
    console.error(`[ELEVENLABS] Error testing RAG indexing endpoints for document ${documentId}:`, error);
    return {
      documentId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    };
  }
}