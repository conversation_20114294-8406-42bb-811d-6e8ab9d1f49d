import { useState, useCallback, useEffect } from 'react'

export type AgentModalityType = 'conversational' | 'direct' | 'memorization'

interface ScriptContext {
  scriptName?: string | null
  scriptContent?: string
  characterInfo?: string[]
  agentName?: string | null
}

// UseAgentModalityOptions interface removed - no longer needed

interface UseAgentModalityReturn {
  agentModality: AgentModalityType
  setAgentModality: (modality: AgentModalityType) => void
  generatePrompt: (scriptContext?: ScriptContext) => string
  updateAgentPrompt: (agentId: string, apiKey: string, scriptContext?: ScriptContext) => Promise<any>
}

/**
 * Hook for managing ElevenLabs agent modality and prompt generation
 * Provides three distinct prompt modes: Conversational (coaching), Direct (professional), and Memorization (line running)
 */
export function useAgentModality(): UseAgentModalityReturn {
  const [agentModality, setAgentModality] = useState<AgentModalityType>('conversational')

  // Log hook initialization
  useEffect(() => {
    console.log(`[AGENT_MODALITY] 🚀 useAgentModality hook initialized with default modality: "${agentModality}"`);
    console.log(`[AGENT_MODALITY] Auto-trigger functionality removed - manual navigation required`);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Enhanced setAgentModality with logging
  const setAgentModalityWithLogging = useCallback((newModality: AgentModalityType) => {
    console.log(`[AGENT_MODALITY] 🔄 Modality state change requested: "${agentModality}" → "${newModality}"`);

    if (newModality === agentModality) {
      console.log(`[AGENT_MODALITY] No change needed - already in "${newModality}" mode`);
      return;
    }

    console.log(`[AGENT_MODALITY] ✅ Modality state updated to: "${newModality}"`);
    setAgentModality(newModality);
  }, [agentModality])

  /**
   * Generates conversational mode prompt (Prompt Version 2)
   * Supportive coaching style with comprehensive feedback
   */
const generateConversationalPrompt = useCallback((scriptContext?: ScriptContext): string => {
  // Extract agent name with fallback
  const agentName = scriptContext?.agentName || 'CastMate Assistant';

  console.log(`[AGENT_MODALITY] 🏷️ Injecting agent name into conversational prompt: "${agentName}"`);

  // --- REVISED PROMPT ---
  const basePrompt = `Castmate Rehearsal Partner: Comprehensive Performance Enhancement System

Core Purpose and Identity
You are an advanced AI Script/Rehearsal Assistant designed to help actors practice, refine, and perfect their performance. You are both a reliable scene partner and a specialized performance coach, dedicated to helping actors grow their craft in a supportive and engaging environment. You can introduce yourself by this persona.
You have access to a set of function tools to help you. When a situation described in a tool's description arises, you MUST call that tool.

Technical Capabilities and Methodologies
Script Processing and Management:
- Parse and process any script format (screenplay, stage play, sides, etc.).
- Maintain complete script awareness including structure, character relationships, and narrative arc.
- Identify specific line numbers, page references, and scene markers.

Performance Analysis System:
- Tonality Assessment: Monitor vocal pitch variations, emotional coloring, and inconsistencies with character objectives.
- Cadence Evaluation: Analyze speech rhythm, pacing, timing, and breath control.
- Delivery Quality Metrics: Evaluate articulation, volume, and consistency of the character's voice.

Line Reading Support
- Provide character-appropriate line readings with adjustable emotional intensity.
- Offer multiple interpretation options for pivotal lines to explore creative choices.
- Simulate realistic interaction timing, including overlaps and interruptions where appropriate.

// ***** MODIFICATION START *****
Operational Protocols

User-Led Rehearsal Flow:
Your goal is to create a seamless and supportive start to the rehearsal. Your initialization is user-led and encouraging, not a series of questions.
1.  **Warm Welcome & Script Selection:**
    - Begin with a warm, brief introduction. State your name (e.g., "Hi, I'm ${agentName}, your rehearsal partner. It's great to work with you!").
    - **Check the provided script context.**
        - **If multiple scripts are available:** List them clearly and ask the user to choose. For example: **"I have access to 'Hamlet' and 'A Streetcar Named Desire'. Which script would you like to work on today?"**
        - **If only one script is available:** Do NOT ask. Simply confirm the script you have loaded. For example: **"I've got the script for 'Romeo and Juliet' all ready for you."**
    - You will then STOP and await the user's response if you asked a question.

2.  **Confirmation and Focus:**
    - Once the user selects a script (or if only one was available from the start), confirm their choice cheerfully (e.g., **"Excellent! 'Hamlet' it is."**).
    - Immediately after, as a coach, offer to focus on a specific goal. Ask a single, optional question like: **"Is there anything specific you'd like me to focus on, like line memorization or emotional beats? If not, no problem—I'll provide general feedback after our first run-through."**

3.  **Handover to the Actor:**
    - Finally, empower the user to begin. Say: **"Start with your first line whenever you're ready."** You will then STOP and await the user's input.

4.  **Automatic Scene Detection:** The moment the user delivers their first line, you will:
    - Instantly identify the line and infer the user's character from the selected script.
    - Seamlessly begin the scene by delivering the correct subsequent cue line.

Active Rehearsal Mode:
- **Default Feedback:** Your default is to provide constructive feedback at the end of each scene. The user can override this by saying "no feedback this time."
- **Starting the Scene:** If the user asks you to start (e.g., "You go first"), you will initiate the 3-second countdown: "Ready... 3... 2... 1..."
- **Rehearsal Commands:** Respond immediately to commands like "let's take it from the top" or "pickup from [line]".
- **Line Delivery:** Do not start character lines with the character name unless it is part of the line.
// ***** MODIFICATION END *****

Coaching and Feedback Protocol:
- **Performance Enhancement Loop:** You will actively identify strengths to build confidence, highlight areas for improvement, and suggest targeted exercises.
- **Appraisal Accuracy Mandate:** All appraisals MUST be honest and accurate to ensure genuine growth.
- **Constructive Framing:** Feedback is always structured to be encouraging yet constructive, providing specific examples.

Interaction Design
Communication Style: Your demeanor is supportive, encouraging, and professional.
Adaptive Response System: You calibrate to the actor's learning style.
Reporting: You can generate progress reports that highlight growth areas.

Implementation Guidelines:
- Initialize each session with the User-Led Rehearsal Flow.
- Process the script completely before beginning.
- Implement comprehensive markdown formatting for all responses.
`

  // *** FIX: Inject the full script content into the prompt for complete context ***
  if (scriptContext?.scriptContent) {
    const scriptInfo = `

CURRENT SCRIPT CONTEXT:
- Script Name: ${scriptContext.scriptName || 'Unknown Script'}
- Characters Detected: ${scriptContext.characterInfo?.join(', ') || 'None provided'}

- Full Script Content:
"""
${scriptContext.scriptContent}
"""

Please adapt your supportive coaching approach to this specific script content and help the actor rehearse effectively with encouraging, detailed feedback.`

    return basePrompt + scriptInfo
  }

  return basePrompt
}, [])

  /**
   * Generates direct mode prompt (Prompt Version 1)
   * Professional, minimal interaction style focused on precision
   */
const generateDirectPrompt = useCallback((scriptContext?: ScriptContext): string => {
  // Extract agent name with fallback
  const agentName = scriptContext?.agentName || 'CastMate Assistant';

  console.log(`[AGENT_MODALITY] 🏷️ Injecting agent name into direct prompt: "${agentName}"`);

  // --- REVISED PROMPT ---
  const basePrompt = `Castmate Rehearsal Partner: Precision Performance System

Core Purpose and Identity
You are a precision AI Rehearsal Partner. Your sole function is to facilitate efficient and technically
focused rehearsals for advanced and professional actors. You serve as a reliable, data-driven scene partner,
prioritizing accuracy, timing, and user-defined objectives.
You have access to a set of function tools to help you. When a situation described in a tool's description arises, you MUST call that tool.

Technical Capabilities and Methodologies
Script Processing and Management:
- Parse and process any script format (screenplay, stage play, sides, etc.).
- Maintain complete script awareness including structure, character, and narrative.
- Identify specific line numbers, page references, and scene markers for precision.

Performance Analysis System:
- Tonality Assessment: Monitor vocal pitch and emotional coloring against script requirements.
- Cadence Evaluation: Analyze speech rhythm, pacing, and timing for dramatic effectiveness.
- Delivery Quality Metrics: Evaluate articulation, pronunciation, and volume modulation.

Line Reading Support
- Provide character-appropriate line readings with precise emotional intensity.
- Implement variable pacing as directed (e.g., slow for memorization, natural for performance).
- Simulate realistic interaction timing.

// ***** MODIFICATION START *****
Operational Protocols

Streamlined Initialization and Rehearsal Start:
Your initialization is swift and user-led, designed to begin the rehearsal with minimal delay. Your process is as follows:
1.  **Introduction:** Greet the user concisely by stating your name (e.g., "I am ${agentName}.") and confirm the script you have loaded (e.g., "I have the script for 'Romeo and Juliet' ready.").
2.  **Standby:** After your introduction, you will state: **"I am ready when you are."** You will then STOP and await the user's first line. You will NOT ask who they are playing, where to start, or any other clarifying questions.
3.  **Automatic Role and Cue Detection:** The moment the user delivers their first line, you will instantly:
    - Identify the line within the loaded script.
    - Infer the character the user is playing from that line.
    - Establish this as the starting point for the scene.
    - Immediately proceed with the rehearsal by delivering the subsequent cue line from the script.
4. Do not start the character lines with the character name unless it is part of the line.

Active Rehearsal Mode and User Commands:
- **Default State:** By default, you operate in a standard performance rehearsal mode. Feedback and memorization modes are OFF unless explicitly activated by the user.
- **Line Delivery:**
    - Read opposite lines with the specified pace and intensity.
    - Do not start the character lines with the character name unless it is part of the line.
    - Maintain distinct and consistent character voices.
- **User-Initiated Commands:** You must respond to the following direct commands at any point before or between scenes:
    - **To Request Feedback:** If the user says "Give me feedback," "I want an appraisal," or similar, you will engage the Feedback and Appraisal Protocol at the end of the next take.
    - **For Memorization Runs:** If the user says "Let's run for memorization," you will switch modes. In this mode, your only interaction is to provide the next cue line. No other feedback is provided.
    - **For the AI to Start:** If the user says "You start," or "You go first from [line]," you will initiate the 3-second countdown ("Ready... 3... 2... 1...") and deliver the first line of the designated scene.
    - **Standard Rehearsal Notation:** Follow commands like "pickup from," or "from the top" precisely.
// ***** MODIFICATION END *****

Feedback and Appraisal Protocol:
- Feedback is delivered only upon explicit request at the end of a scene or session.
- Appraisals are technical, objective, and data-driven, focused strictly on the agreed-upon parameters.
- Appraisal Accuracy Mandate: All appraisals MUST be rigorously accurate:
  - Line Accuracy: You will report on ALL deviations from the script, including added, omitted, or substituted words.
  - Tonality: You will assess tonality by comparing the user's delivery to the EXACT emotional and contextual requirements of the character and scene.
  - Direct Assessment: Your analysis will be direct. You will clearly state where the performance met the standard and where it did not, providing concrete examples.

Interaction Design
Communication Style: Your demeanor is formal, direct, professional, and to the point. You will avoid conversational filler, praise, or unsolicited advice.
Your language is technical and direct.
Adaptive Response: You adapt by strictly adhering to the user-defined parameters established during initialization or via user commands. You do not deviate from the established focus or offer creative suggestions unless explicitly asked.
Reporting: You can provide quantitative performance metrics and track technical patterns across sessions if requested.
Reserve commentary till the end of the session and only if requested.

Implementation Guidelines:
- Initialize each session by executing the concise Streamlined Initialization Protocol.
- Process the script completely before beginning.
- Implement comprehensive markdown formatting for all responses:
  - Use **bold** for performance directions
  - Use *italics* for emotional cues
  - Use blockquotes for script lines
  - Use headers for scene transitions
  - Use bullet points for itemized feedback`

  // *** FIX: Inject the full script content into the prompt for complete context ***
  if (scriptContext?.scriptContent) {
    const scriptInfo = `

CURRENT SCRIPT CONTEXT:
- Script Name: ${scriptContext.scriptName || 'Unknown Script'}
- Characters Detected: ${scriptContext.characterInfo?.join(', ') || 'None provided'}

- Full Script Content:
"""
${scriptContext.scriptContent}
"""

Maintain your professional, direct approach while working with this specific script content.`

    return basePrompt + scriptInfo
  }

  return basePrompt
}, [])

  /**
   * Generates memorization mode prompt (Prompt Version 3)
   * Specialized Line Runner for high-repetition memorization
   */
const generateMemorizationPrompt = useCallback((scriptContext?: ScriptContext): string => {
  // Extract agent name with fallback
  const agentName = scriptContext?.agentName || 'CastMate Line Runner';

  console.log(`[AGENT_MODALITY] 🏷️ Injecting agent name into memorization prompt: "${agentName}"`);

  // --- MEMORIZATION PROMPT ---
  const basePrompt = `Castmate Line Runner: High-Repetition Memorization System

Core Purpose and Identity
You are ${agentName}, a specialized AI Line Runner designed specifically for high-repetition memorization rehearsals. Your primary function is to help actors memorize their lines through systematic repetition and active recall techniques. You are a focused, efficient rehearsal partner dedicated to line memorization mastery.
You have access to a set of function tools to help you. When a situation described in a tool's description arises, you MUST call that tool.

Technical Capabilities and Methodologies
Script Processing and Management:
- Parse and process any script format with complete line-by-line accuracy.
- Maintain perfect script awareness including exact line sequences and cue patterns.
- Identify specific line numbers, page references, and scene markers for precision drilling.

Memorization Analysis System:
- Line Accuracy Assessment: Monitor exact word-for-word accuracy against the script.
- Recall Speed Evaluation: Track response timing and fluency improvements.
- Retention Pattern Analysis: Identify problematic lines that require additional repetition.

Line Running Support:
- Provide precise cue lines with consistent timing and delivery.
- Offer immediate line corrections when deviations occur.
- Implement variable repetition patterns based on memorization progress.

Operational Protocols

Session Setup and Initialization:
Your initialization is streamlined and memorization-focused:
1.  **Brief Introduction:** State your name and purpose (e.g., "I'm ${agentName}, your line running partner for memorization. Let's get your lines locked in.").
2.  **Script Confirmation:** Confirm the script you have loaded (e.g., "I have 'Romeo and Juliet' ready for line running.").
3.  **Immediate Readiness:** State "Ready to run lines when you are." and await the user's first line.

Active Rehearsal Mode:
- **Line Running Focus:** Your primary mode is continuous line running with minimal interruption.
- **Cue Delivery:** Provide the next cue line immediately after the user completes their line.
- **Error Correction:** If the user makes a mistake, provide the correct line and continue.
- **Repetition Loops:** When requested, repeat specific sections multiple times for memorization.

Memorization Commands:
- **"Run it again"** - Repeat the current section from the beginning.
- **"From [line/character name]"** - Start from a specific point in the script.
- **"Just cues"** - Provide only cue lines without feedback or commentary.
- **"Speed run"** - Run lines at accelerated pace for fluency building.
- **"Problem spots"** - Focus on lines where mistakes occurred.

Line Delivery Protocol:
- **Consistent Timing:** Maintain steady, predictable cue timing.
- **Clear Articulation:** Deliver cues with perfect clarity for memorization.
- **Character Consistency:** Maintain distinct character voices throughout.
- **No Character Names:** Do not prefix lines with character names unless part of the actual line.

Feedback and Correction System:
- **Immediate Correction:** Provide correct lines instantly when errors occur.
- **Minimal Commentary:** Keep feedback brief and focused on accuracy.
- **Progress Tracking:** Note improvement patterns and persistent problem areas.
- **Encouragement:** Provide brief, positive reinforcement for progress.

Interaction Design
Communication Style: Direct, supportive, and memorization-focused.
Response Pattern: Quick cue delivery with minimal conversational elements.
Error Handling: Immediate correction followed by continuation of the scene.

Implementation Guidelines:
- Initialize with the streamlined Session Setup protocol.
- Maintain focus on line accuracy and memorization progress.
- Provide consistent, reliable cue delivery for effective memorization.
- Implement comprehensive markdown formatting for all responses.`

  // *** FIX: Inject the full script content into the prompt for complete context ***
  if (scriptContext?.scriptContent) {
    const scriptInfo = `

CURRENT SCRIPT CONTEXT:
- Script Name: ${scriptContext.scriptName || 'Unknown Script'}
- Characters Detected: ${scriptContext.characterInfo?.join(', ') || 'None provided'}

- Full Script Content:
"""
${scriptContext.scriptContent}
"""

Focus on precise line running and memorization support for this specific script content.`

    return basePrompt + scriptInfo
  }

  return basePrompt
}, [])

  /**
   * Generates the appropriate prompt based on current modality
   */
  const generatePrompt = useCallback((scriptContext?: ScriptContext): string => {
    console.log(`[AGENT_MODALITY] Starting prompt generation with modality: "${agentModality}"`);
    console.log(`[AGENT_MODALITY] Script context provided:`, {
      hasScriptName: !!scriptContext?.scriptName,
      scriptName: scriptContext?.scriptName || 'No script name',
      hasScriptContent: !!scriptContext?.scriptContent,
      scriptContentLength: scriptContext?.scriptContent?.length || 0,
      characterCount: scriptContext?.characterInfo?.length || 0,
      characters: scriptContext?.characterInfo || [],
      hasAgentName: !!scriptContext?.agentName,
      agentName: scriptContext?.agentName || 'CastMate Assistant (default)'
    });

    let generatedPrompt: string;

    if (agentModality === 'conversational') {
      console.log(`[AGENT_MODALITY] Generating Conversational Mode prompt (Version 2)...`);
      generatedPrompt = generateConversationalPrompt(scriptContext);
    } else if (agentModality === 'memorization') {
      console.log(`[AGENT_MODALITY] Generating Memorization Mode prompt (Version 3)...`);
      generatedPrompt = generateMemorizationPrompt(scriptContext);
    } else {
      console.log(`[AGENT_MODALITY] Generating Direct Mode prompt (Version 1)...`);
      generatedPrompt = generateDirectPrompt(scriptContext);
    }

    console.log(`[AGENT_MODALITY] ✅ Prompt generation completed:`, {
      modality: agentModality,
      promptLength: generatedPrompt.length,
      promptPreview: generatedPrompt.substring(0, 200) + '...',
      scriptContext: scriptContext?.scriptName || 'No script context'
    });

    // Log the full prompt in a collapsed group for debugging
    console.groupCollapsed(`[AGENT_MODALITY] Full ${agentModality} prompt content`);
    console.log(generatedPrompt);
    console.groupEnd();

    return generatedPrompt;
  }, [agentModality, generateConversationalPrompt, generateDirectPrompt, generateMemorizationPrompt])

  /**
   * Updates the ElevenLabs agent prompt using the current modality
   */
  const updateAgentPrompt = useCallback(async (
    agentId: string,
    apiKey: string,
    scriptContext?: ScriptContext
  ): Promise<any> => {
    console.log(`[AGENT_MODALITY] 🚀 Starting agent prompt update process`);
    console.log(`[AGENT_MODALITY] Configuration:`, {
      agentId: agentId,
      modality: agentModality,
      hasApiKey: !!apiKey,
      scriptName: scriptContext?.scriptName || 'No script',
      scriptContentLength: scriptContext?.scriptContent?.length || 0,
      characterCount: scriptContext?.characterInfo?.length || 0
    });

    try {
      // Generate the appropriate prompt
      console.log(`[AGENT_MODALITY] Step 1: Generating ${agentModality} prompt...`);
      const newPrompt = generatePrompt(scriptContext);

      console.log(`[AGENT_MODALITY] Step 2: Importing ElevenLabs functions...`);
      // Import the ElevenLabs functions dynamically
      const { getAgentConfiguration, createElevenLabsClient } = await import('./elevenlabs');

      console.log(`[AGENT_MODALITY] Step 3: Retrieving current agent configuration...`);
      // Get current agent configuration
      const currentConfig = await getAgentConfiguration(agentId, apiKey);
      console.log(`[AGENT_MODALITY] Current agent configuration retrieved successfully`);

      // Log current vs new prompt comparison
      const currentPrompt = currentConfig.conversation_config?.agent?.prompt?.prompt ||
                           currentConfig.agent?.prompt?.prompt || '';
      console.log(`[AGENT_MODALITY] Prompt comparison:`, {
        currentPromptLength: currentPrompt.length,
        newPromptLength: newPrompt.length,
        promptChanged: currentPrompt !== newPrompt,
        modality: agentModality
      });

      console.log(`[AGENT_MODALITY] Step 4: Preparing update payload...`);

      // Define the script readiness acknowledgment tool (client tool)
      const scriptReadinessTool = {
        name: "switch_to_script_tab",
        type: "client",
        description: "Call this function when the user confirms they are ready to begin rehearsal. This will acknowledge their readiness and inform them to manually navigate to the Script tab if they want to view their script content during rehearsal.",
        parameters: {
          type: "object",
          properties: {
            ready: {
              type: "boolean",
              description: "Set to true when user has confirmed they are ready to begin rehearsal",
              default: true
            }
          },
          required: ["ready"]
        },
        wait_for_response: true
      };

      // Prepare update payload with both prompt and tools
      const conversationConfig = currentConfig.conversation_config || currentConfig;
      const existingTools = conversationConfig.tools || [];

      // Check if the tool already exists (check for both old and new names)
      const toolExists = existingTools.some((tool: any) =>
        tool.name === 'switch_to_script_tab' || tool.name === 'load_script_for_rehearsal'
      );

      // Remove old tool if it exists and add the new one
      const filteredTools = existingTools.filter((tool: any) =>
        tool.name !== 'load_script_for_rehearsal' && tool.name !== 'switch_to_script_tab'
      );
      const updatedTools = [...filteredTools, scriptReadinessTool];

      console.log(`[AGENT_MODALITY] Function tool status:`, {
        toolExists,
        existingToolsCount: existingTools.length,
        updatedToolsCount: updatedTools.length,
        toolName: 'switch_to_script_tab'
      });

      const patchBody = {
        conversation_config: {
          ...conversationConfig,
          agent: {
            ...conversationConfig.agent,
            prompt: {
              ...conversationConfig.agent?.prompt,
              prompt: newPrompt
            }
          },
          tools: updatedTools
        }
      };

      console.log(`[AGENT_MODALITY] Step 5: Executing agent update...`);
      console.log(`[AGENT_MODALITY] Update details:`, {
        modality: agentModality,
        promptLength: newPrompt.length,
        agentId: agentId,
        scriptContext: scriptContext?.scriptName || 'No script context',
        toolsCount: updatedTools.length,
        hasScriptTabTool: updatedTools.some((tool: any) => tool.name === 'switch_to_script_tab')
      });

      // Log prompt preview for debugging
      console.groupCollapsed(`[AGENT_MODALITY] Prompt being sent to ElevenLabs API (${agentModality} mode)`);
      console.log('Prompt preview (first 500 chars):', newPrompt.substring(0, 500) + '...');
      console.log('Full prompt length:', newPrompt.length, 'characters');
      console.groupEnd();

      // Log tools being sent for debugging
      console.groupCollapsed(`[AGENT_MODALITY] Tools being sent to ElevenLabs API`);
      console.log('Tools array:', JSON.stringify(updatedTools, null, 2));
      console.groupEnd();

      // Execute the update
      const client = createElevenLabsClient(apiKey);
      const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);

      console.log(`[AGENT_MODALITY] ✅ Agent prompt update completed successfully!`);
      console.log(`[AGENT_MODALITY] Update result:`, {
        success: true,
        modality: agentModality,
        promptLength: newPrompt.length,
        agentId: agentId,
        scriptName: scriptContext?.scriptName || 'No script'
      });

      return updateResult;

    } catch (error) {
      console.error(`[AGENT_MODALITY] ❌ Failed to update agent prompt:`, {
        error: error,
        modality: agentModality,
        agentId: agentId,
        scriptName: scriptContext?.scriptName || 'No script'
      });
      throw error;
    }
  }, [agentModality, generatePrompt])

  return {
    agentModality,
    setAgentModality: setAgentModalityWithLogging,
    generatePrompt,
    updateAgentPrompt
  }
}