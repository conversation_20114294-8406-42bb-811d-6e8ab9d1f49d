// types/questionnaire.ts

// This interface defines the shape of the form's state.
// Note how it matches the different types of questions.
export interface QuestionnaireFormData {
  // --- Section: General Feedback ---
  overallExperience: string; // radio
  improvementAspects: string; // textarea
  
  // --- Section: Feature Evaluation ---
  valuableFeatures: string[]; // checkbox (multi-select)
  satisfactionRatings: Record<string, string>; // ratingScale (nested object)
  
  // --- Section: Final Thoughts ---
  wouldRecommend: string; // radio
  finalComments: string; // textarea
}

// This interface defines a single sub-question for a rating scale.
export interface SubQuestion {
  id: string;
  label: string;
}

// This is the master interface for a question. It's designed to be flexible.
export interface Question {
  id: keyof QuestionnaireFormData;
  section: string;
  question: string;
  type: 'textarea' | 'radio' | 'checkbox' | 'ratingScale';
  required: boolean;
  placeholder?: string;
  maxLength?: number;
  options?: string[]; // For radio and checkbox
  maxSelections?: number; // For checkbox
  subQuestions?: SubQuestion[]; // For ratingScale
}

// This is the single source of truth for the entire questionnaire.
export const questionnaireQuestions: Question[] = [
  {
    id: 'overallExperience',
    section: 'General Feedback',
    question: 'How would you rate your overall experience with the app?',
    type: 'radio',
    options: ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor'],
    required: true,
  },
  {
    id: 'improvementAspects',
    section: 'General Feedback',
    question: 'What is the one thing we could do to improve the app for you?',
    type: 'textarea',
    required: true,
    placeholder: 'Please be specific...',
    maxLength: 500,
  },
  {
    id: 'valuableFeatures',
    section: 'Feature Evaluation',
    question: 'Which features did you find most valuable? (Select up to 3)',
    type: 'checkbox',
    options: ['Script Upload', 'AI Scene Partner', 'Voice Customization', 'Line Memorization Helper', 'Performance Analytics'],
    required: true,
    maxSelections: 3,
  },
  {
    id: 'satisfactionRatings',
    section: 'Feature Evaluation',
    question: 'Please rate your satisfaction with the following aspects:',
    type: 'ratingScale',
    required: true,
    subQuestions: [
      { id: 'userInterface', label: 'Ease of Use and Navigation' },
      { id: 'voiceQuality', label: 'AI Voice Model Quality' },
      { id: 'responsiveness', label: 'App Speed and Responsiveness' },
    ],
  },
  {
    id: 'wouldRecommend',
    section: 'Final Thoughts',
    question: 'How likely are you to recommend this app to a friend or colleague?',
    type: 'radio',
    options: ['Very Likely', 'Likely', 'Neutral', 'Unlikely', 'Very Unlikely'],
    required: true,
  },
  {
    id: 'finalComments',
    section: 'Final Thoughts',
    question: 'Any final comments or suggestions?',
    type: 'textarea',
    required: false, // This one is optional
    placeholder: 'Share any other thoughts...',
    maxLength: 1000,
  },
];


// This type remains mostly the same, but references the corrected FormData
export interface QuestionnaireSubmission {
  id?: string;
  userId: string;
  userEmail: string;
  userName?: string;
  responses: QuestionnaireFormData;
  timestamp: Date; // This will be replaced by serverTimestamp on submission
  metadata?: {
    userAgent?: string;
    sessionId?: string;
  };
}